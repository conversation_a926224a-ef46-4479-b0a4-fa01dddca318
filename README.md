# Synthetic Monitoring Application

A comprehensive synthetic monitoring solution using Grafana OSS, Prometheus, and a custom ICMP ping monitoring service with OpenTelemetry instrumentation.

## Architecture

- **Grafana OSS**: Visualization and dashboards
- **Prometheus**: Metrics storage and collection
- **Synthetic Monitor**: Custom Python application for ICMP ping monitoring
- **Docker Compose**: Container orchestration

## Features

- ICMP ping monitoring with RTT measurements
- Web interface for probe configuration
- Prometheus metrics export
- OpenTelemetry instrumentation
- Grafana dashboards for visualization
- Persistent configuration storage
- Real-time monitoring status

## Quick Start

1. **Build and start the services:**
   ```bash
   docker-compose up --build
   ```

2. **Access the services:**
   - Synthetic Monitor Web UI: http://localhost:8080
   - Prometheus: http://localhost:9090
   - Grafana: http://localhost:3000 (admin/admin)

3. **Configure probes:**
   - Use the web interface at http://localhost:8080 to add/remove/configure ping probes
   - Default probes are pre-configured for Google DNS, Cloudflare DNS, and Google.com

## Services

### Synthetic Monitor (Port 8080)
- Web interface for probe management
- REST API for probe configuration
- Metrics endpoint at `/metrics` (Prometheus format)
- OpenTelemetry metrics at `/otel/metrics`

### Prometheus (Port 9090)
- Scrapes metrics from synthetic monitor every 10 seconds
- 15-day retention policy
- Web UI for metric exploration

### Grafana (Port 3000)
- Pre-configured Prometheus datasource
- Synthetic monitoring dashboard
- Default credentials: admin/admin

## Metrics

The application exports the following metrics:

### Prometheus Metrics
- `synthetic_ping_duration_seconds`: Histogram of ping durations
- `synthetic_ping_success_total`: Counter of successful pings
- `synthetic_ping_failure_total`: Counter of failed pings
- `synthetic_ping_rtt_seconds`: Current RTT gauge
- `synthetic_probe_status`: Probe status (1=up, 0=down)

### OpenTelemetry Metrics
- Same metrics as above, exported via OpenTelemetry SDK
- Available at `/otel/metrics` endpoint

## Configuration

### Probe Configuration
Probes are configured via the web interface or by editing `synthetic-monitor/config/probes.json`:

```json
{
  "probes": [
    {
      "name": "google-dns",
      "target": "8.8.8.8",
      "interval": 30,
      "timeout": 5,
      "enabled": true
    }
  ]
}
```

### Prometheus Configuration
Edit `prometheus/prometheus.yml` to modify scraping behavior.

### Grafana Configuration
- Datasources: `grafana/provisioning/datasources/`
- Dashboards: `grafana/dashboards/`

## API Endpoints

### Synthetic Monitor API
- `GET /api/probes` - List all probes
- `POST /api/probes` - Create new probe
- `PUT /api/probes/{name}` - Update probe
- `DELETE /api/probes/{name}` - Delete probe
- `GET /metrics` - Prometheus metrics
- `GET /otel/metrics` - OpenTelemetry metrics

## Development

### Requirements
- Docker and Docker Compose
- Python 3.11+ (for local development)

### Local Development
1. Install dependencies:
   ```bash
   cd synthetic-monitor
   pip install -r requirements.txt
   ```

2. Run locally:
   ```bash
   python main.py
   ```

### Building
```bash
docker-compose build
```

## Monitoring Dashboard

The Grafana dashboard includes:
- Real-time RTT graphs
- Probe status indicators
- Success/failure rate pie charts
- RTT percentile analysis

## Troubleshooting

1. **Ping permissions**: The container runs with `NET_RAW` capability for ICMP ping
2. **Port conflicts**: Ensure ports 3000, 8080, 8081, 9090 are available
3. **DNS resolution**: Verify target hostnames resolve correctly
4. **Firewall**: Ensure ICMP traffic is allowed

## Security Notes

- Default Grafana credentials should be changed in production
- The synthetic monitor runs as non-root user
- ICMP requires NET_RAW capability (minimal privilege escalation)

## License

MIT License
