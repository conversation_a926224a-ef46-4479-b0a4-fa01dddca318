<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Synthetic Monitor</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .form-section {
            background-color: #f9f9f9;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .delete-btn {
            background-color: #dc3545;
        }
        .delete-btn:hover {
            background-color: #c82333;
        }
        .probes-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .probes-table th, .probes-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .probes-table th {
            background-color: #f2f2f2;
        }
        .status-up {
            color: #28a745;
            font-weight: bold;
        }
        .status-down {
            color: #dc3545;
            font-weight: bold;
        }
        .status-unknown {
            color: #6c757d;
        }
        .metrics-link {
            display: inline-block;
            margin: 10px 0;
            padding: 10px 15px;
            background-color: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 4px;
        }
        .metrics-link:hover {
            background-color: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Synthetic Monitor Dashboard</h1>
        
        <div class="form-section">
            <h2>Add New Probe</h2>
            <form id="addProbeForm">
                <div class="form-group">
                    <label for="name">Probe Name:</label>
                    <input type="text" id="name" name="name" required>
                </div>
                <div class="form-group">
                    <label for="target">Target (IP or hostname):</label>
                    <input type="text" id="target" name="target" required placeholder="******* or google.com">
                </div>
                <div class="form-group">
                    <label for="interval">Interval (seconds):</label>
                    <input type="number" id="interval" name="interval" value="30" min="5" max="3600">
                </div>
                <div class="form-group">
                    <label for="timeout">Timeout (seconds):</label>
                    <input type="number" id="timeout" name="timeout" value="5" min="1" max="30">
                </div>
                <button type="submit">Add Probe</button>
            </form>
        </div>

        <div>
            <h2>Active Probes</h2>
            <a href="/metrics" class="metrics-link" target="_blank">View Prometheus Metrics</a>
            <table class="probes-table" id="probesTable">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Target</th>
                        <th>Interval</th>
                        <th>Timeout</th>
                        <th>Status</th>
                        <th>Last RTT</th>
                        <th>Last Check</th>
                        <th>Enabled</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="probesTableBody">
                    <!-- Probes will be loaded here -->
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // Load probes on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadProbes();
            setInterval(loadProbes, 5000); // Refresh every 5 seconds
        });

        // Add probe form submission
        document.getElementById('addProbeForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            try {
                const response = await fetch('/api/probes', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    alert('Probe added successfully!');
                    this.reset();
                    loadProbes();
                } else {
                    const error = await response.json();
                    alert('Error: ' + error.detail);
                }
            } catch (error) {
                alert('Error adding probe: ' + error.message);
            }
        });

        // Load probes from API
        async function loadProbes() {
            try {
                const response = await fetch('/api/probes');
                const probes = await response.json();
                
                const tbody = document.getElementById('probesTableBody');
                tbody.innerHTML = '';
                
                probes.forEach(probe => {
                    const row = document.createElement('tr');
                    
                    const statusClass = probe.status === 'up' ? 'status-up' : 
                                       probe.status === 'down' ? 'status-down' : 'status-unknown';
                    
                    const lastRtt = probe.last_rtt ? (probe.last_rtt * 1000).toFixed(2) + ' ms' : 'N/A';
                    const lastCheck = probe.last_check ? new Date(probe.last_check).toLocaleString() : 'N/A';
                    
                    row.innerHTML = `
                        <td>${probe.name}</td>
                        <td>${probe.target}</td>
                        <td>${probe.interval}s</td>
                        <td>${probe.timeout}s</td>
                        <td class="${statusClass}">${probe.status}</td>
                        <td>${lastRtt}</td>
                        <td>${lastCheck}</td>
                        <td>
                            <input type="checkbox" ${probe.enabled ? 'checked' : ''} 
                                   onchange="toggleProbe('${probe.name}', this.checked)">
                        </td>
                        <td>
                            <button class="delete-btn" onclick="deleteProbe('${probe.name}')">Delete</button>
                        </td>
                    `;
                    
                    tbody.appendChild(row);
                });
            } catch (error) {
                console.error('Error loading probes:', error);
            }
        }

        // Toggle probe enabled/disabled
        async function toggleProbe(probeName, enabled) {
            try {
                const formData = new FormData();
                formData.append('enabled', enabled);
                
                const response = await fetch(`/api/probes/${probeName}`, {
                    method: 'PUT',
                    body: formData
                });
                
                if (!response.ok) {
                    const error = await response.json();
                    alert('Error: ' + error.detail);
                    loadProbes(); // Reload to reset checkbox
                }
            } catch (error) {
                alert('Error updating probe: ' + error.message);
                loadProbes(); // Reload to reset checkbox
            }
        }

        // Delete probe
        async function deleteProbe(probeName) {
            if (confirm(`Are you sure you want to delete probe "${probeName}"?`)) {
                try {
                    const response = await fetch(`/api/probes/${probeName}`, {
                        method: 'DELETE'
                    });
                    
                    if (response.ok) {
                        alert('Probe deleted successfully!');
                        loadProbes();
                    } else {
                        const error = await response.json();
                        alert('Error: ' + error.detail);
                    }
                } catch (error) {
                    alert('Error deleting probe: ' + error.message);
                }
            }
        }
    </script>
</body>
</html>
