#!/usr/bin/env python3

import asyncio
import json
import os
import time
from typing import Dict, List, Optional
from datetime import datetime
import logging

from fastapi import FastAPI, Request, Form, HTTPException
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
import uvicorn

from prometheus_client import Counter, Histogram, Gauge, generate_latest, CONTENT_TYPE_LATEST
from prometheus_client import start_http_server
import ping3

# OpenTelemetry imports (simplified for this implementation)
# from opentelemetry import metrics
# from opentelemetry.sdk.metrics import MeterProvider
# from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
# from opentelemetry.exporter.prometheus import PrometheusMetricReader
# from opentelemetry.instrumentation.fastapi import FastAPIInstrumentor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize OpenTelemetry (commented out for now)
# prometheus_reader = PrometheusMetricReader()
# meter_provider = MeterProvider(metric_readers=[prometheus_reader])
# metrics.set_meter_provider(meter_provider)
# meter = metrics.get_meter(__name__)

# Prometheus metrics
ping_duration_histogram = Histogram(
    'synthetic_ping_duration_seconds',
    'Time taken for ping in seconds',
    ['target', 'probe_name']
)

ping_success_counter = Counter(
    'synthetic_ping_success_total',
    'Total successful pings',
    ['target', 'probe_name']
)

ping_failure_counter = Counter(
    'synthetic_ping_failure_total',
    'Total failed pings',
    ['target', 'probe_name']
)

ping_rtt_gauge = Gauge(
    'synthetic_ping_rtt_seconds',
    'Current RTT for ping in seconds',
    ['target', 'probe_name']
)

probe_status_gauge = Gauge(
    'synthetic_probe_status',
    'Status of probe (1=up, 0=down)',
    ['target', 'probe_name']
)

# OpenTelemetry metrics (commented out for now - using Prometheus client only)
# otel_ping_duration = meter.create_histogram(
#     name="synthetic_ping_duration_seconds",
#     description="Time taken for ping in seconds",
#     unit="s"
# )

# otel_ping_success = meter.create_counter(
#     name="synthetic_ping_success_total",
#     description="Total successful pings"
# )

# otel_ping_failure = meter.create_counter(
#     name="synthetic_ping_failure_total",
#     description="Total failed pings"
# )

# otel_ping_rtt = meter.create_gauge(
#     name="synthetic_ping_rtt_seconds",
#     description="Current RTT for ping in seconds",
#     unit="s"
# )

# otel_probe_status = meter.create_gauge(
#     name="synthetic_probe_status",
#     description="Status of probe (1=up, 0=down)"
# )

class ProbeConfig:
    def __init__(self, name: str, target: str, interval: int = 30, timeout: int = 5):
        self.name = name
        self.target = target
        self.interval = interval
        self.timeout = timeout
        self.enabled = True
        self.last_rtt = None
        self.last_check = None
        self.status = "unknown"

class SyntheticMonitor:
    def __init__(self):
        self.probes: Dict[str, ProbeConfig] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
        self.config_file = "/app/config/probes.json"
        self.load_config()

    def load_config(self):
        """Load probe configuration from file"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    data = json.load(f)
                    for probe_data in data.get('probes', []):
                        probe = ProbeConfig(
                            name=probe_data['name'],
                            target=probe_data['target'],
                            interval=probe_data.get('interval', 30),
                            timeout=probe_data.get('timeout', 5)
                        )
                        probe.enabled = probe_data.get('enabled', True)
                        self.probes[probe.name] = probe
                logger.info(f"Loaded {len(self.probes)} probes from config")
        except Exception as e:
            logger.error(f"Error loading config: {e}")

    def save_config(self):
        """Save probe configuration to file"""
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            data = {
                'probes': [
                    {
                        'name': probe.name,
                        'target': probe.target,
                        'interval': probe.interval,
                        'timeout': probe.timeout,
                        'enabled': probe.enabled
                    }
                    for probe in self.probes.values()
                ]
            }
            with open(self.config_file, 'w') as f:
                json.dump(data, f, indent=2)
            logger.info("Configuration saved")
        except Exception as e:
            logger.error(f"Error saving config: {e}")

    async def ping_target(self, probe: ProbeConfig) -> Optional[float]:
        """Perform ICMP ping to target and return RTT in seconds"""
        try:
            # ping3.ping returns RTT in seconds or None if failed
            rtt = ping3.ping(probe.target, timeout=probe.timeout)
            return rtt
        except Exception as e:
            logger.error(f"Ping error for {probe.target}: {e}")
            return None

    async def monitor_probe(self, probe: ProbeConfig):
        """Continuously monitor a single probe"""
        logger.info(f"Starting monitoring for probe {probe.name} -> {probe.target}")
        
        while probe.enabled:
            try:
                start_time = time.time()
                rtt = await self.ping_target(probe)
                
                probe.last_check = datetime.now()
                
                if rtt is not None:
                    # Successful ping
                    probe.last_rtt = rtt
                    probe.status = "up"

                    # Update Prometheus metrics
                    ping_duration_histogram.labels(target=probe.target, probe_name=probe.name).observe(rtt)
                    ping_success_counter.labels(target=probe.target, probe_name=probe.name).inc()
                    ping_rtt_gauge.labels(target=probe.target, probe_name=probe.name).set(rtt)
                    probe_status_gauge.labels(target=probe.target, probe_name=probe.name).set(1)

                    # Update OpenTelemetry metrics (commented out for now)
                    # otel_ping_duration.record(rtt, {"target": probe.target, "probe_name": probe.name})
                    # otel_ping_success.add(1, {"target": probe.target, "probe_name": probe.name})
                    # otel_ping_rtt.set(rtt, {"target": probe.target, "probe_name": probe.name})
                    # otel_probe_status.set(1, {"target": probe.target, "probe_name": probe.name})

                    logger.debug(f"Probe {probe.name}: RTT={rtt:.3f}s")
                else:
                    # Failed ping
                    probe.status = "down"
                    ping_failure_counter.labels(target=probe.target, probe_name=probe.name).inc()
                    probe_status_gauge.labels(target=probe.target, probe_name=probe.name).set(0)

                    # Update OpenTelemetry metrics (commented out for now)
                    # otel_ping_failure.add(1, {"target": probe.target, "probe_name": probe.name})
                    # otel_probe_status.set(0, {"target": probe.target, "probe_name": probe.name})

                    logger.warning(f"Probe {probe.name}: Ping failed")
                
                # Wait for next interval
                await asyncio.sleep(probe.interval)
                
            except asyncio.CancelledError:
                logger.info(f"Monitoring cancelled for probe {probe.name}")
                break
            except Exception as e:
                logger.error(f"Error in monitor_probe for {probe.name}: {e}")
                await asyncio.sleep(probe.interval)

    def start_probe(self, probe_name: str):
        """Start monitoring a probe"""
        if probe_name in self.probes and probe_name not in self.running_tasks:
            probe = self.probes[probe_name]
            if probe.enabled:
                task = asyncio.create_task(self.monitor_probe(probe))
                self.running_tasks[probe_name] = task
                logger.info(f"Started probe {probe_name}")

    def stop_probe(self, probe_name: str):
        """Stop monitoring a probe"""
        if probe_name in self.running_tasks:
            self.running_tasks[probe_name].cancel()
            del self.running_tasks[probe_name]
            logger.info(f"Stopped probe {probe_name}")

    def add_probe(self, name: str, target: str, interval: int = 30, timeout: int = 5):
        """Add a new probe"""
        if name in self.probes:
            raise ValueError(f"Probe {name} already exists")
        
        probe = ProbeConfig(name, target, interval, timeout)
        self.probes[name] = probe
        self.save_config()
        self.start_probe(name)
        return probe

    def remove_probe(self, name: str):
        """Remove a probe"""
        if name not in self.probes:
            raise ValueError(f"Probe {name} not found")
        
        self.stop_probe(name)
        del self.probes[name]
        self.save_config()

    def update_probe(self, name: str, target: str = None, interval: int = None, timeout: int = None, enabled: bool = None):
        """Update probe configuration"""
        if name not in self.probes:
            raise ValueError(f"Probe {name} not found")
        
        probe = self.probes[name]
        restart_needed = False
        
        if target is not None and target != probe.target:
            probe.target = target
            restart_needed = True
        
        if interval is not None and interval != probe.interval:
            probe.interval = interval
            restart_needed = True
            
        if timeout is not None and timeout != probe.timeout:
            probe.timeout = timeout
            restart_needed = True
            
        if enabled is not None and enabled != probe.enabled:
            probe.enabled = enabled
            restart_needed = True
        
        self.save_config()
        
        if restart_needed:
            self.stop_probe(name)
            if probe.enabled:
                self.start_probe(name)

    def start_all_probes(self):
        """Start all enabled probes"""
        for probe_name, probe in self.probes.items():
            if probe.enabled:
                self.start_probe(probe_name)

# Global monitor instance
monitor = SyntheticMonitor()

# FastAPI app
app = FastAPI(title="Synthetic Monitor", description="ICMP Ping Monitoring with Prometheus metrics")

# Instrument FastAPI with OpenTelemetry (commented out for now)
# FastAPIInstrumentor.instrument_app(app)

# Templates
templates = Jinja2Templates(directory="/app/templates")

@app.on_event("startup")
async def startup_event():
    """Start all probes on startup"""
    monitor.start_all_probes()
    logger.info("Synthetic Monitor started")

@app.on_event("shutdown")
async def shutdown_event():
    """Stop all probes on shutdown"""
    for probe_name in list(monitor.running_tasks.keys()):
        monitor.stop_probe(probe_name)
    logger.info("Synthetic Monitor stopped")

# Metrics endpoint
@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint"""
    return generate_latest().decode('utf-8'), 200, {'Content-Type': CONTENT_TYPE_LATEST}

@app.get("/otel/metrics")
async def otel_metrics():
    """OpenTelemetry metrics endpoint (placeholder for now)"""
    # The PrometheusMetricReader will handle the export
    return {"message": "OpenTelemetry metrics endpoint - not implemented yet"}

# API endpoints
@app.get("/api/probes")
async def get_probes():
    """Get all probes"""
    probes_data = []
    for probe in monitor.probes.values():
        probes_data.append({
            'name': probe.name,
            'target': probe.target,
            'interval': probe.interval,
            'timeout': probe.timeout,
            'enabled': probe.enabled,
            'status': probe.status,
            'last_rtt': probe.last_rtt,
            'last_check': probe.last_check.isoformat() if probe.last_check else None
        })
    return probes_data

@app.post("/api/probes")
async def create_probe(
    name: str = Form(...),
    target: str = Form(...),
    interval: int = Form(30),
    timeout: int = Form(5)
):
    """Create a new probe"""
    try:
        probe = monitor.add_probe(name, target, interval, timeout)
        return {"message": f"Probe {name} created successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.put("/api/probes/{probe_name}")
async def update_probe(
    probe_name: str,
    target: str = Form(None),
    interval: int = Form(None),
    timeout: int = Form(None),
    enabled: bool = Form(None)
):
    """Update a probe"""
    try:
        monitor.update_probe(probe_name, target, interval, timeout, enabled)
        return {"message": f"Probe {probe_name} updated successfully"}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

@app.delete("/api/probes/{probe_name}")
async def delete_probe(probe_name: str):
    """Delete a probe"""
    try:
        monitor.remove_probe(probe_name)
        return {"message": f"Probe {probe_name} deleted successfully"}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))

# Web interface
@app.get("/", response_class=HTMLResponse)
async def index(request: Request):
    """Main web interface"""
    return templates.TemplateResponse("index.html", {"request": request, "probes": monitor.probes})

if __name__ == "__main__":
    # Start metrics server on separate port
    start_http_server(8081)
    
    # Start web server
    uvicorn.run(app, host="0.0.0.0", port=8080)
