global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # Synthetic monitoring metrics
  - job_name: 'synthetic-monitor'
    static_configs:
      - targets: ['synthetic-monitor:8081']
    scrape_interval: 10s
    metrics_path: '/metrics'
    
  # Additional scrape config for OpenTelemetry metrics if needed
  - job_name: 'synthetic-monitor-otel'
    static_configs:
      - targets: ['synthetic-monitor:8081']
    scrape_interval: 10s
    metrics_path: '/otel/metrics'

# Alerting configuration (optional)
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# Storage configuration is handled via command line flags in docker-compose.yml
